import sharp from 'sharp'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { vercelPostgresAdapter } from '@payloadcms/db-vercel-postgres'
import { buildConfig } from 'payload'
import { Users } from '@/app/collections/Users'
import { Articles } from '@/app/collections/Articles'
import { Media } from '@/app/collections/Media'
import { Sources } from '@/app/collections/Sources'


export default buildConfig({
  // If you'd like to use Rich Text, pass your editor here
  editor: lexicalEditor(),

  // Admin configuration
  admin: {
    user: Users.slug,
    meta: {
      titleSuffix: '- Tech News CMS',
    },
  },

  // Define and configure your collections in this array
  collections: [
    Users,
    Articles,
    Media,
    Sources,
  ],

  // Your Payload secret - should be a complex and secure string, unguessable
  secret: process.env.PAYLOAD_SECRET || '',
  // Whichever Database Adapter you're using should go here
  // Mongoose is shown as an example, but you can also use Postgres
  db: vercelPostgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URL,
    },
    forceUseVercelPostgres: true,
  }),
  // If you want to resize images, crop, set focal point, etc.
  // make sure to install it and pass it to the config.
  // This is optional - if you don't need to do these things,
  // you don't need it!
  sharp,
})