import type { NextConfig } from "next";
import { withPayload } from '@payloadcms/next/withPayload'

const nextConfig: NextConfig = {
  /* config options here */
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'o.aolcdn.com',
                port: '',
                pathname: '/**',
                search: '',
            },
        ],
    },
    // Ensure proper caching for sitemap and robots.txt
    headers: async () => {
        return [
            {
                source: '/sitemap.xml',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=3600, s-maxage=86400',
                    },
                ],
            },
            {
                source: '/robots.txt',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'public, max-age=3600, s-maxage=86400',
                    },
                ],
            },
        ];
    },
   //  turbopack: {},
    
};

// export default nextConfig;
export default withPayload(nextConfig);