/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:db-schema` to regenerate this file.
 */

import type {} from "@payloadcms/db-vercel-postgres";
import {
  pgTable,
  index,
  uniqueIndex,
  foreignKey,
  serial,
  timestamp,
  varchar,
  numeric,
  jsonb,
  boolean,
  integer,
  pgEnum,
} from "@payloadcms/db-vercel-postgres/drizzle/pg-core";
import { sql, relations } from "@payloadcms/db-vercel-postgres/drizzle";
export const enum_articles_post_type = pgEnum("enum_articles_post_type", [
  "ARTICLE",
  "VIDEOYT",
  "VIDEOOTHER",
]);
export const enum_articles_status = pgEnum("enum_articles_status", [
  "draft",
  "published",
  "archived",
]);

export const users = pgTable(
  "users",
  {
    id: serial("id").primaryKey(),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    email: varchar("email").notNull(),
    resetPasswordToken: varchar("reset_password_token"),
    resetPasswordExpiration: timestamp("reset_password_expiration", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    }),
    salt: varchar("salt"),
    hash: varchar("hash"),
    loginAttempts: numeric("login_attempts").default("0"),
    lockUntil: timestamp("lock_until", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    }),
  },
  (columns) => ({
    users_updated_at_idx: index("users_updated_at_idx").on(columns.updatedAt),
    users_created_at_idx: index("users_created_at_idx").on(columns.createdAt),
    users_email_idx: uniqueIndex("users_email_idx").on(columns.email),
  }),
);

export const articles = pgTable(
  "articles",
  {
    id: serial("id").primaryKey(),
    title: varchar("title").notNull(),
    post_type: enum_articles_post_type("post_type")
      .notNull()
      .default("ARTICLE"),
    content: jsonb("content"),
    summary: varchar("summary"),
    summarization_vendor: varchar("summarization_vendor"),
    slug: varchar("slug").notNull(),
    url: varchar("url").notNull(),
    author: varchar("author"),
    source_id: numeric("source_id").notNull(),
    tags: varchar("tags"),
    published_date: timestamp("published_date", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    }).notNull(),
    thumbnail_key: varchar("thumbnail_key"),
    views: numeric("views").default("0"),
    video_id: varchar("video_id"),
    status: enum_articles_status("status").notNull().default("draft"),
    featured: boolean("featured").default(false),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    articles_slug_idx: uniqueIndex("articles_slug_idx").on(columns.slug),
    articles_url_idx: uniqueIndex("articles_url_idx").on(columns.url),
    articles_updated_at_idx: index("articles_updated_at_idx").on(
      columns.updatedAt,
    ),
    articles_created_at_idx: index("articles_created_at_idx").on(
      columns.createdAt,
    ),
  }),
);

export const media = pgTable(
  "media",
  {
    id: serial("id").primaryKey(),
    alt: varchar("alt").notNull(),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    url: varchar("url"),
    thumbnailURL: varchar("thumbnail_u_r_l"),
    filename: varchar("filename"),
    mimeType: varchar("mime_type"),
    filesize: numeric("filesize"),
    width: numeric("width"),
    height: numeric("height"),
    focalX: numeric("focal_x"),
    focalY: numeric("focal_y"),
    sizes_thumbnail_url: varchar("sizes_thumbnail_url"),
    sizes_thumbnail_width: numeric("sizes_thumbnail_width"),
    sizes_thumbnail_height: numeric("sizes_thumbnail_height"),
    sizes_thumbnail_mimeType: varchar("sizes_thumbnail_mime_type"),
    sizes_thumbnail_filesize: numeric("sizes_thumbnail_filesize"),
    sizes_thumbnail_filename: varchar("sizes_thumbnail_filename"),
    sizes_card_url: varchar("sizes_card_url"),
    sizes_card_width: numeric("sizes_card_width"),
    sizes_card_height: numeric("sizes_card_height"),
    sizes_card_mimeType: varchar("sizes_card_mime_type"),
    sizes_card_filesize: numeric("sizes_card_filesize"),
    sizes_card_filename: varchar("sizes_card_filename"),
  },
  (columns) => ({
    media_updated_at_idx: index("media_updated_at_idx").on(columns.updatedAt),
    media_created_at_idx: index("media_created_at_idx").on(columns.createdAt),
    media_filename_idx: uniqueIndex("media_filename_idx").on(columns.filename),
    media_sizes_thumbnail_sizes_thumbnail_filename_idx: index(
      "media_sizes_thumbnail_sizes_thumbnail_filename_idx",
    ).on(columns.sizes_thumbnail_filename),
    media_sizes_card_sizes_card_filename_idx: index(
      "media_sizes_card_sizes_card_filename_idx",
    ).on(columns.sizes_card_filename),
  }),
);

export const sources = pgTable(
  "sources",
  {
    id: serial("id").primaryKey(),
    title: varchar("title").notNull(),
    site: varchar("site").notNull(),
    rss_url: varchar("rss_url").notNull(),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    sources_updated_at_idx: index("sources_updated_at_idx").on(
      columns.updatedAt,
    ),
    sources_created_at_idx: index("sources_created_at_idx").on(
      columns.createdAt,
    ),
  }),
);

export const payload_locked_documents = pgTable(
  "payload_locked_documents",
  {
    id: serial("id").primaryKey(),
    globalSlug: varchar("global_slug"),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_locked_documents_global_slug_idx: index(
      "payload_locked_documents_global_slug_idx",
    ).on(columns.globalSlug),
    payload_locked_documents_updated_at_idx: index(
      "payload_locked_documents_updated_at_idx",
    ).on(columns.updatedAt),
    payload_locked_documents_created_at_idx: index(
      "payload_locked_documents_created_at_idx",
    ).on(columns.createdAt),
  }),
);

export const payload_locked_documents_rels = pgTable(
  "payload_locked_documents_rels",
  {
    id: serial("id").primaryKey(),
    order: integer("order"),
    parent: integer("parent_id").notNull(),
    path: varchar("path").notNull(),
    usersID: integer("users_id"),
    articlesID: integer("articles_id"),
    mediaID: integer("media_id"),
    sourcesID: integer("sources_id"),
  },
  (columns) => ({
    order: index("payload_locked_documents_rels_order_idx").on(columns.order),
    parentIdx: index("payload_locked_documents_rels_parent_idx").on(
      columns.parent,
    ),
    pathIdx: index("payload_locked_documents_rels_path_idx").on(columns.path),
    payload_locked_documents_rels_users_id_idx: index(
      "payload_locked_documents_rels_users_id_idx",
    ).on(columns.usersID),
    payload_locked_documents_rels_articles_id_idx: index(
      "payload_locked_documents_rels_articles_id_idx",
    ).on(columns.articlesID),
    payload_locked_documents_rels_media_id_idx: index(
      "payload_locked_documents_rels_media_id_idx",
    ).on(columns.mediaID),
    payload_locked_documents_rels_sources_id_idx: index(
      "payload_locked_documents_rels_sources_id_idx",
    ).on(columns.sourcesID),
    parentFk: foreignKey({
      columns: [columns["parent"]],
      foreignColumns: [payload_locked_documents.id],
      name: "payload_locked_documents_rels_parent_fk",
    }).onDelete("cascade"),
    usersIdFk: foreignKey({
      columns: [columns["usersID"]],
      foreignColumns: [users.id],
      name: "payload_locked_documents_rels_users_fk",
    }).onDelete("cascade"),
    articlesIdFk: foreignKey({
      columns: [columns["articlesID"]],
      foreignColumns: [articles.id],
      name: "payload_locked_documents_rels_articles_fk",
    }).onDelete("cascade"),
    mediaIdFk: foreignKey({
      columns: [columns["mediaID"]],
      foreignColumns: [media.id],
      name: "payload_locked_documents_rels_media_fk",
    }).onDelete("cascade"),
    sourcesIdFk: foreignKey({
      columns: [columns["sourcesID"]],
      foreignColumns: [sources.id],
      name: "payload_locked_documents_rels_sources_fk",
    }).onDelete("cascade"),
  }),
);

export const payload_preferences = pgTable(
  "payload_preferences",
  {
    id: serial("id").primaryKey(),
    key: varchar("key"),
    value: jsonb("value"),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_preferences_key_idx: index("payload_preferences_key_idx").on(
      columns.key,
    ),
    payload_preferences_updated_at_idx: index(
      "payload_preferences_updated_at_idx",
    ).on(columns.updatedAt),
    payload_preferences_created_at_idx: index(
      "payload_preferences_created_at_idx",
    ).on(columns.createdAt),
  }),
);

export const payload_preferences_rels = pgTable(
  "payload_preferences_rels",
  {
    id: serial("id").primaryKey(),
    order: integer("order"),
    parent: integer("parent_id").notNull(),
    path: varchar("path").notNull(),
    usersID: integer("users_id"),
  },
  (columns) => ({
    order: index("payload_preferences_rels_order_idx").on(columns.order),
    parentIdx: index("payload_preferences_rels_parent_idx").on(columns.parent),
    pathIdx: index("payload_preferences_rels_path_idx").on(columns.path),
    payload_preferences_rels_users_id_idx: index(
      "payload_preferences_rels_users_id_idx",
    ).on(columns.usersID),
    parentFk: foreignKey({
      columns: [columns["parent"]],
      foreignColumns: [payload_preferences.id],
      name: "payload_preferences_rels_parent_fk",
    }).onDelete("cascade"),
    usersIdFk: foreignKey({
      columns: [columns["usersID"]],
      foreignColumns: [users.id],
      name: "payload_preferences_rels_users_fk",
    }).onDelete("cascade"),
  }),
);

export const payload_migrations = pgTable(
  "payload_migrations",
  {
    id: serial("id").primaryKey(),
    name: varchar("name"),
    batch: numeric("batch"),
    updatedAt: timestamp("updated_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
    createdAt: timestamp("created_at", {
      mode: "string",
      withTimezone: true,
      precision: 3,
    })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_migrations_updated_at_idx: index(
      "payload_migrations_updated_at_idx",
    ).on(columns.updatedAt),
    payload_migrations_created_at_idx: index(
      "payload_migrations_created_at_idx",
    ).on(columns.createdAt),
  }),
);

export const relations_users = relations(users, () => ({}));
export const relations_articles = relations(articles, () => ({}));
export const relations_media = relations(media, () => ({}));
export const relations_sources = relations(sources, () => ({}));
export const relations_payload_locked_documents_rels = relations(
  payload_locked_documents_rels,
  ({ one }) => ({
    parent: one(payload_locked_documents, {
      fields: [payload_locked_documents_rels.parent],
      references: [payload_locked_documents.id],
      relationName: "_rels",
    }),
    usersID: one(users, {
      fields: [payload_locked_documents_rels.usersID],
      references: [users.id],
      relationName: "users",
    }),
    articlesID: one(articles, {
      fields: [payload_locked_documents_rels.articlesID],
      references: [articles.id],
      relationName: "articles",
    }),
    mediaID: one(media, {
      fields: [payload_locked_documents_rels.mediaID],
      references: [media.id],
      relationName: "media",
    }),
    sourcesID: one(sources, {
      fields: [payload_locked_documents_rels.sourcesID],
      references: [sources.id],
      relationName: "sources",
    }),
  }),
);
export const relations_payload_locked_documents = relations(
  payload_locked_documents,
  ({ many }) => ({
    _rels: many(payload_locked_documents_rels, {
      relationName: "_rels",
    }),
  }),
);
export const relations_payload_preferences_rels = relations(
  payload_preferences_rels,
  ({ one }) => ({
    parent: one(payload_preferences, {
      fields: [payload_preferences_rels.parent],
      references: [payload_preferences.id],
      relationName: "_rels",
    }),
    usersID: one(users, {
      fields: [payload_preferences_rels.usersID],
      references: [users.id],
      relationName: "users",
    }),
  }),
);
export const relations_payload_preferences = relations(
  payload_preferences,
  ({ many }) => ({
    _rels: many(payload_preferences_rels, {
      relationName: "_rels",
    }),
  }),
);
export const relations_payload_migrations = relations(
  payload_migrations,
  () => ({}),
);

type DatabaseSchema = {
  enum_articles_post_type: typeof enum_articles_post_type;
  enum_articles_status: typeof enum_articles_status;
  users: typeof users;
  articles: typeof articles;
  media: typeof media;
  sources: typeof sources;
  payload_locked_documents: typeof payload_locked_documents;
  payload_locked_documents_rels: typeof payload_locked_documents_rels;
  payload_preferences: typeof payload_preferences;
  payload_preferences_rels: typeof payload_preferences_rels;
  payload_migrations: typeof payload_migrations;
  relations_users: typeof relations_users;
  relations_articles: typeof relations_articles;
  relations_media: typeof relations_media;
  relations_sources: typeof relations_sources;
  relations_payload_locked_documents_rels: typeof relations_payload_locked_documents_rels;
  relations_payload_locked_documents: typeof relations_payload_locked_documents;
  relations_payload_preferences_rels: typeof relations_payload_preferences_rels;
  relations_payload_preferences: typeof relations_payload_preferences;
  relations_payload_migrations: typeof relations_payload_migrations;
};

declare module "@payloadcms/db-vercel-postgres" {
  export interface GeneratedDatabaseSchema {
    schema: DatabaseSchema;
  }
}
