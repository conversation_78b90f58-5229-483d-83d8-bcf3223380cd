import { useEffect } from 'react';
export const usePushStateListener = (callback) => {
    useEffect(() => {
        const originalPushState = history.pushState;

        history.pushState = function(data, title, url ) {
            originalPushState.apply(history, [data, title, url]);
            callback(url);

            return () => {
                history.pushState = originalPushState;
            };
        }

    }, [callback]);
}