import PropTypes from 'prop-types';

export const useSlug = (input: string): string | null => {
    const from = 'àáäâãåăąæçćęèéëêǵḧìíïîḿńǹñòóöôœøṕŕßśșțùúüûǘẃẍÿżź·/_,:;';
    const to = 'aaaaaaaaacceeeeeghiiiimnnnooooooprssstuuuuuwxyzz------';
    const regex = new RegExp(from.split('').join('|'), 'g');
    
    if (!input || typeof input !== 'string') return null;
  
    return input
      .toString()
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(regex, (character) => to.charAt(from.indexOf(character)))
      .replace(/&/g, '-and-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '');
  };
  
  useSlug.propTypes = {
    input: PropTypes.string.isRequired,
  };