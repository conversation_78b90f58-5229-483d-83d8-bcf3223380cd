export default function ArticleSkeleton() {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden animate-pulse">
      {/* Thumbnail Skeleton */}
      <div className="aspect-video w-full bg-gray-200 dark:bg-gray-700" />

      <div className="p-4 space-y-4">
        {/* Category Skeleton */}
        <div className="w-24 h-6 bg-gray-200 dark:bg-gray-700 rounded-full" />

        {/* Title Skeleton */}
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4" />
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2" />
        </div>

        {/* Summary Skeleton */}
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded" />
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6" />
        </div>

        {/* Metadata Skeleton */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex space-x-4">
            <div className="w-20 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="w-2 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
          <div className="flex space-x-4">
            <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="w-8 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="w-4 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="w-4 h-3 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
}
