"use client";

import { useState, useEffect} from "react";
import { cn } from "@/lib/utils";

export default function CalltoActionBanner({
    demo  = false,
    message = 'Our website is still in development—thank you for stopping by!',
    onClickCallback = () => {}
})
{
    const [isOpen, setIsOpen] = useState(false)
    const [hide, setHide] = useState(false)

    const click = () => {
        setIsOpen(false);
        const date = new Date();
        date.setTime(date.getTime() + (24 * 60 * 60 * 1000));
        document.cookie = 'cookieUnderdev=true; expires=' + date.toUTCString();
        setTimeout(() => {
            setHide(true)
        }, 700)
        onClickCallback();
    }

    useEffect(() => {
        try {
            if (!document.cookie.includes('cookieUnderdev')) {
                setIsOpen(true);
            }
        }
        catch (err) {

        }
    }, []);

    return (
        <div className={
            cn("z-[500] fixed top-0 left-0 right-0 duration-700",
                !isOpen
            ? "transition-[opacity,transform] translate-y-0 opacity-0"
            : "transition-[opacity,transform] translate-y-0 opacity-100",
                hide && "hidden"
            )}
        >
            <div className="bg-blue-600 text-white py-3 px-4 flex justify-between items-center">
                <div className="flex-1 text-center">
                    <p className="text-sm md:text-base">
                        { message }
                    </p>
                </div>
                <button
                    className="bg-white text-blue-600 font-semibold py-2 px-4 rounded-full hover:bg-blue-50 transition duration-300"
                    onClick={click}
                >
                    Close
                </button>
            </div>
        </div>


    );

}