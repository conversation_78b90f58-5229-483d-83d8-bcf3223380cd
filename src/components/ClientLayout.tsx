"use client";

import { ReactNode } from 'react';
import ClientSidebar from './ClientSidebar';
import LayoutContent from './LayoutContent'
import ThemeToggle from './ThemeToggle';
import UserMenu from './UserMenu';

export default function ClientLayout({ children }: { children: ReactNode }) {
    return (
        <div className="flex">
            <ClientSidebar />
            <LayoutContent>{children}</LayoutContent>
        </div>
    );
}