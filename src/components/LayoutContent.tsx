import { ReactNode, useState, useEffect } from 'react';
import ThemeToggle from './ThemeToggle';
import UserMenu from './UserMenu';

export default function LayoutContent({ children }: { children: ReactNode }) {
    const [isMobile, setIsMobile] = useState(false);
    const [sidebarOpen, setSidebarOpen] = useState(true);

    useEffect(() => {
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        const handleSidebarToggle = (e: CustomEvent) => {
            setSidebarOpen(e.detail.isOpen);
        };

        // Initialize
        checkScreenSize();

        // Set up event listeners
        window.addEventListener('resize', checkScreenSize);
        window.addEventListener('sidebarToggle', handleSidebarToggle as EventListener);

        return () => {
            window.removeEventListener('resize', checkScreenSize);
            window.removeEventListener('sidebarToggle', handleSidebarToggle as EventListener);
        };
    }, []);

    return (
        <div className={`flex-1 ${isMobile ? 'ml-0' : (sidebarOpen ? 'ml-64' : 'ml-0')} transition-all duration-300`}>
            <header
                className={`h-16 fixed top-0 ${isMobile ? 'left-0' : (sidebarOpen ? 'left-64' : 'left-0')} right-0 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 z-10 transition-all duration-300`}>
                <div className="flex items-center justify-between h-full px-6">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Tech News Feed</h1>
                    <div className="flex items-center space-x-4">
                        <ThemeToggle/>
                        <UserMenu/>
                    </div>
                </div>
            </header>
            <main className="pt-16">
                {children}
            </main>
        </div>
    );
}