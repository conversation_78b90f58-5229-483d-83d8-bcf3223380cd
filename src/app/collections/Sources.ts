import type { CollectionConfig } from 'payload'

export const Sources: CollectionConfig = {
  slug: 'sources',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'site', 'rss_url'],
    group: 'Content',
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'The name of the news source (e.g., TechCrunch)',
      },
    },
    {
      name: 'site',
      type: 'text',
      required: true,
      admin: {
        description: 'The website domain (e.g., techcrunch.com)',
      },
    },
    {
      name: 'rss_url',
      type: 'text',
      required: true,
      admin: {
        description: 'RSS feed URL for this source',
      },
    },
  ],
}
