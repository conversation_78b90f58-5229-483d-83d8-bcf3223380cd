import { ArrowLeft, Heart, MessageCircle, Share2, Clock, Globe } from 'lucide-react';
import Link from 'next/link';
// import { useParams } from 'next/navigation';
import { Article, getArticleBySlug } from '../../utils/article'
import { Metadata } from 'next';
import ClientArticlePage from './client-page';


export async function generateMetadata({ params }) : Promise<Metadata> {
  // This runs on the server and is SEO-friendly
  const { slug } = await params;
  
  const article = await getArticleBySlug(slug);
  
  return {
    title: `${article.title} - Tech News Aggregator`,
    description: article.summary.substring(0, 160),
    openGraph: {
      title: `${article.title} - Tech News Aggregator`,
      description: article.summary.substring(0, 160),
      images: [{ url: "/og-image.jpg"}] ,
    },
    twitter: {
      card: 'summary_large_image',
      title: article.title,
      description: article.summary || 'Read the latest technology news and updates.',
      images: article.thumbnail_key ? [article.thumbnail_key] : ['/twitter-card.jpg'],
    },
  };
}


export default async function ArticlePage({ params }) {
 //  const params = useParams(); 
  const { slug } = await params;

  // Pre-fetch the article on the server
  const article = await getArticleBySlug(slug);
  
  // Pass the pre-fetched article to the client component
  return <ClientArticlePage initialArticle={article} slug={slug} />;
}