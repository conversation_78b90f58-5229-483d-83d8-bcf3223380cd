import {NextRequest, NextResponse} from "next/server";
import {Article, getLatestArticles} from "@/app/(frontend)/utils/article";
import {prisma} from "@/db/prisma";
import {put} from "@vercel/blob";

// import { list } from '@vercel/blob'
export async function GET() {
    try {
        // 1. Fetch all articles with thumbnail URLs
        const articles = await prisma.article.findMany({
            where: {
                thumbnail_key: {
                    not: null
                },
                source_id: 10
            },
            select: {
                id: true,
                thumbnail_key: true
            }
        });

        console.log(`Found ${articles.length} articles with thumbnails to migrate`);

        // 2. Process each article
        const results = await Promise.all(
            articles.map(async (article) => {
                try {
                    if (!article.thumbnail_key) return { id: article.id, status: 'skipped', reason: 'no-thumbnail' };

                    // 3. Download the image
                    const imageResponse = await fetch(article.thumbnail_key);

                    if (!imageResponse.ok) {
                        return {
                            id: article.id,
                            status: 'error',
                            reason: `Failed to download: ${imageResponse.status}`
                        };
                    }



                    // 4. Generate a filename based on article ID and original extension
                    const urlParts = new URL(article.thumbnail_key).pathname.split('.');
                    const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1] : 'jpg';
                    const filename = `article-${article.id}-thumbnail.${extension}`;

                    const contentType = imageResponse.headers.get('content-type') || `image/${extension}`;

                    console.info('here')
                    const imageBuffer = await imageResponse.arrayBuffer();

                    console.info('after')

                    // 5. Upload to Vercel Blob
                    const blob = await put(filename, imageBuffer, {
                        access: 'public',
                        contentType: contentType,
                    });

                    // 6. Update the article record with the new Blob URL
                    await prisma.article.update({
                        where: { id: article.id },
                        data: {
                            thumbnail_key: blob.url,
                            // Optionally store the original URL in another field
                        },
                    });

                    return {
                        id: article.id,
                        status: 'success',
                        oldUrl: article.thumbnail_key,
                        newUrl: blob.url
                    };

                } catch (error) {
                    console.error(`Error processing article ${article.id}:`, error);
                    return {
                        id: article.id,
                        status: 'error',
                        reason: error instanceof Error ? error.message : 'Unknown error'
                    };
                }
            })
        );

        // 7. Return summary of the operation
        const summary = {
            total: articles.length,
            succeeded: results.filter(r => r.status === 'success').length,
            failed: results.filter(r => r.status === 'error').length,
            skipped: results.filter(r => r.status === 'skipped').length,
            details: results
        };

        return NextResponse.json(summary);
    } catch (error) {
        console.error('Migration failed:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Unknown error' },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}