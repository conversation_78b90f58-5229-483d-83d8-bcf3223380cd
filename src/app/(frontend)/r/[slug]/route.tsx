import {NextRequest, NextResponse} from "next/server";
import {getArticleBySlug, incrViews} from "@/app/(frontend)/utils/article";

export async function GET(request: NextRequest, { params }) {
    const { slug } = await params
    const post  = await getArticleBySlug(slug);

    if (post) {
        incrViews(post.id); // incr only once per session
        return NextResponse.redirect(post.url, {
            status: 308,
            statusText: 'Permanent Redirect'
        });
    }

    return NextResponse.redirect(new URL('/', request.url));
}