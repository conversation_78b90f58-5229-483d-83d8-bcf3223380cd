import { MetadataRoute } from 'next';
import { prisma } from '@/db/prisma';

/**
 * Generate a sitemap for Next.js
 * This uses Next.js built-in sitemap generation
 * https://nextjs.org/docs/app/api-reference/file-conventions/metadata/sitemap
 */
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Get base URL from environment or default to production URL
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://tech-news.io';

  try {
    // Get all articles
    const articles = await prisma.article.findMany({
      select: {
        slug: true,
        published_date: true,
      },
      orderBy: {
        published_date: 'desc',
      },
      take: 5000, // Limit to 5000 most recent articles
    });

    // Get all unique tags
    const tagsResult = await prisma.article.findMany({
      select: {
        tags: true,
      },
    });

    // Flatten and get unique tags
    const allTags = tagsResult.flatMap(article => article.tags);
    const tags = Array.from(new Set(allTags));

    // Create sitemap entries for static pages
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
      // Add other important static pages here
      {
        url: `${baseUrl}/auth/login`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
      {
        url: `${baseUrl}/auth/register`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.3,
      },
    ];

    // Create sitemap entries for articles
    const articlePages: MetadataRoute.Sitemap = articles.map(article => ({
      url: `${baseUrl}/article/${article.slug}`,
      lastModified: article.published_date,
      changeFrequency: 'monthly',
      priority: 0.7,
    }));

    // Create sitemap entries for tags
    const tagPages: MetadataRoute.Sitemap = tags.map(tag => ({
      url: `${baseUrl}/tag/${encodeURIComponent(tag)}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.5,
    }));

    // Combine all entries
    return [...staticPages, ...articlePages, ...tagPages];
  } catch (error) {
    console.error('Error generating sitemap:', error);

    // Return a minimal sitemap with just the homepage if there's an error
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
    ];
  } finally {
    await prisma.$disconnect();
  }
}
