import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email, password } = body;

    // TODO: Add proper validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // TODO: Add proper authentication and database integration
    // For now, simulate a successful login with dummy data
    if (email === '<EMAIL>' && password === 'password') {
      return NextResponse.json({
        user: {
          id: '1',
          name: 'Test User',
          email,
          createdAt: new Date().toISOString()
        },
        token: 'dummy-jwt-token'
      });
    }

    return NextResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    );
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
