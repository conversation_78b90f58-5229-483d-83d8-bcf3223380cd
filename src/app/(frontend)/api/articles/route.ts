import { NextResponse } from 'next/server';
import { newsData, NewsArticle } from '@/data/news';
import {getLatestArticles} from "@/app/(frontend)/utils/article";

/*function getFilteredArticles(filter: string): NewsArticle[] {
  switch (filter) {
    case 'Popular':
      return [...newsData].sort((a, b) => b.likes - a.likes);
    case 'Following':
      // In a real app, this would filter based on user's followed sources/categories
      return newsData.filter(article => 
        ['TechCrunch', 'The Verge'].includes(article.source)
      );
    case 'Latest':
    default:
      // Assuming the data is already sorted by timestamp
      return newsData;
  }
}*/

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const filter = searchParams.get('filter') || 'Latest';

 //  const filteredArticles = getFilteredArticles(filter);
 //  const startIndex = (page - 1) * limit;
//   const endIndex = startIndex + limit;
  const paginatedArticles = await getLatestArticles(page,  filter, '');  // filteredArticles.slice(startIndex, endIndex);
  // const hasMore = endIndex < filteredArticles.length;

//   console.log(paginatedArticles)

  return NextResponse.json(paginatedArticles);
}
