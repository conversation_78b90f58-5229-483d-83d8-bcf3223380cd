import { NextResponse } from 'next/server';
import { newsData } from '@/data/news';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ tag: string }> }
) {
  const { searchParams } = new URL(request.url);
  const { tag } = await params;
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const _tag = decodeURIComponent(tag);

  // Simulate server delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Filter articles by tag/category
  const filteredArticles = newsData.filter(
    article => article.category.toLowerCase() === _tag.toLowerCase()
  );

  // Calculate pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedArticles = filteredArticles.slice(startIndex, endIndex);
  const hasMore = endIndex < filteredArticles.length;

  return NextResponse.json({
    articles: paginatedArticles,
    hasMore,
    nextPage: hasMore ? page + 1 : null,
    total: filteredArticles.length,
    tag
  });
}
