import { Pool, neonConfig } from '@neondatabase/serverless'
import { PrismaNeon } from '@prisma/adapter-neon'
import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'
import ws from 'ws'

dotenv.config()
// neonConfig.webSocketConstructor = ws
neonConfig.webSocketConstructor = ws

/*const connectionString =  process.env.DATABASE_URL;

if (!connectionString) {
    throw new Error('DATABASE_URL is not defined')
}*/

const connectionString = `${process.env.DATABASE_URL}`
const adapter = new PrismaNeon({ connectionString })

export const prisma = new PrismaClient({
    adapter,
    log: ['query', 'info', 'warn', 'error']
})