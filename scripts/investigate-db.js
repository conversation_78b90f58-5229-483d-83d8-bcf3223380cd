import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function investigateDatabase() {
  try {
    console.log('🔍 Investigating database structure...\n');
    
    // List all tables
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    
    console.log('📋 All tables in database:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
    // Check if there are any tables with 'article' in the name
    const articleTables = await prisma.$queryRaw`
      SELECT table_name, 
             (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
      FROM information_schema.tables t
      WHERE table_schema = 'public' 
      AND table_name ILIKE '%article%'
      ORDER BY table_name;
    `;
    
    console.log('\n📰 Tables containing "article":');
    if (articleTables.length === 0) {
      console.log('  No tables found with "article" in the name');
    } else {
      articleTables.forEach(table => {
        console.log(`  - ${table.table_name} (${table.column_count} columns)`);
      });
    }
    
    // Check for any tables with data
    console.log('\n📊 Tables with data:');
    for (const table of tables) {
      try {
        const count = await prisma.$queryRawUnsafe(`SELECT COUNT(*) as count FROM "${table.table_name}"`);
        if (count[0].count > 0) {
          console.log(`  - ${table.table_name}: ${count[0].count} rows`);
        }
      } catch (error) {
        // Skip tables we can't query
      }
    }
    
    // Check recent migrations
    console.log('\n🔄 Recent migrations:');
    try {
      const migrations = await prisma.$queryRaw`
        SELECT name, batch, created_at 
        FROM payload_migrations 
        ORDER BY created_at DESC 
        LIMIT 5;
      `;
      
      if (migrations.length === 0) {
        console.log('  No migrations found');
      } else {
        migrations.forEach(migration => {
          console.log(`  - ${migration.name} (batch ${migration.batch}) - ${migration.created_at}`);
        });
      }
    } catch (error) {
      console.log('  Could not read migrations table');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

investigateDatabase();
