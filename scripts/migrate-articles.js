import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateArticles() {
  console.log('Starting article migration...');

  try {
    // Get all articles
    const articles = await prisma.$queryRaw`
      SELECT id, title, content, summary, tags, published_date, views
      FROM articles
      LIMIT 10
    `;

    console.log(`Found ${articles.length} articles to migrate`);

    for (const article of articles) {
      console.log(`Processing article: ${article.title}`);

      // Convert plain text content to Payload's richText JSON format
      const richTextContent = {
        root: {
          type: 'root',
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'text',
                  text: article.content || '',
                  version: 1
                }
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      // Log what we would update (don't actually update yet)
      console.log(`Would update article ${article.id}:`);
      console.log(`- Content length: ${article.content?.length || 0} chars`);
      console.log(`- Tags: ${article.tags}`);
      console.log(`- Views: ${article.views}`);
    }

    console.log('Migration preview completed. No data was changed.');

  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateArticles();
