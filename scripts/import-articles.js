import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import { parse } from 'csv-parse/sync';

const prisma = new PrismaClient();

async function importArticles() {
  try {
    console.log('🚀 Starting article import from CSV...\n');
    
    // Read the CSV file
    const csvContent = fs.readFileSync('scripts/articles-2025-05-29 (3).csv', 'utf-8');
    
    // Parse CSV
    const records = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      cast: (value, context) => {
        // Handle empty values
        if (value === '' || value === 'NULL') return null;
        
        // Handle specific columns
        if (context.column === 'id' || context.column === 'source_id' || context.column === 'views') {
          return value ? parseInt(value) : null;
        }
        
        // Handle tags array - convert from PostgreSQL array format
        if (context.column === 'tags') {
          if (value && value.startsWith('{') && value.endsWith('}')) {
            // Remove braces and split by comma
            return value.slice(1, -1).split(',').map(tag => tag.trim().replace(/"/g, '')).join(',');
          }
          return value;
        }
        
        // Handle dates
        if (context.column === 'published_date') {
          return value ? new Date(value) : null;
        }
        
        return value;
      }
    });
    
    console.log(`📊 Found ${records.length} articles in CSV`);
    
    // Import in batches to avoid memory issues
    const batchSize = 100;
    let imported = 0;
    let errors = 0;
    
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(records.length / batchSize)} (${batch.length} articles)`);
      
      try {
        // Prepare data for Prisma
        const articlesToInsert = batch.map(record => ({
          id: record.id,
          title: record.title,
          post_type: record.post_type || 'ARTICLE',
          content: record.content || '',
          summary: record.summary,
          summarization_vendor: record.summarization_vendor,
          slug: record.slug,
          url: record.url,
          author: record.author,
          source_id: record.source_id,
          tags: record.tags, // Now as comma-separated string
          published_date: record.published_date,
          thumbnail_key: record.thumbnail_key,
          views: record.views,
          video_id: record.video_id,
          updated_at: new Date(),
          created_at: new Date()
        }));
        
        // Insert batch using createMany
        await prisma.article.createMany({
          data: articlesToInsert,
          skipDuplicates: true // Skip if ID already exists
        });
        
        imported += batch.length;
        
      } catch (error) {
        console.error(`❌ Error in batch ${Math.floor(i / batchSize) + 1}:`, error.message);
        errors += batch.length;
      }
    }
    
    console.log(`\n✅ Import completed!`);
    console.log(`📊 Successfully imported: ${imported} articles`);
    console.log(`❌ Errors: ${errors} articles`);
    
    // Verify the import
    const totalCount = await prisma.article.count();
    console.log(`🔍 Total articles in database: ${totalCount}`);
    
  } catch (error) {
    console.error('❌ Import failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

importArticles();
