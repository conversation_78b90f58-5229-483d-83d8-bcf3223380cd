import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkArticles() {
  try {
    console.log('🔍 Checking your articles...\n');
    
    // Count total articles
    const totalCount = await prisma.article.count();
    console.log(`📊 Total articles in database: ${totalCount}`);
    
    if (totalCount === 0) {
      console.log('❌ No articles found in the database!');
      
      // Check if the table exists
      try {
        const tableInfo = await prisma.$queryRaw`
          SELECT column_name, data_type 
          FROM information_schema.columns 
          WHERE table_name = 'articles'
          ORDER BY ordinal_position;
        `;
        
        if (tableInfo.length > 0) {
          console.log('\n📋 Articles table structure:');
          tableInfo.forEach(col => {
            console.log(`  - ${col.column_name}: ${col.data_type}`);
          });
        } else {
          console.log('❌ Articles table does not exist!');
        }
      } catch (error) {
        console.log('❌ Error checking table structure:', error.message);
      }
      
    } else {
      // Show sample articles
      const sampleArticles = await prisma.article.findMany({
        take: 5,
        select: {
          id: true,
          title: true,
          published_date: true,
          thumbnail_key: true,
          source_id: true
        },
        orderBy: {
          published_date: 'desc'
        }
      });
      
      console.log('\n📰 Sample articles:');
      sampleArticles.forEach(article => {
        console.log(`  ${article.id}: ${article.title.substring(0, 50)}...`);
        console.log(`     Published: ${article.published_date}`);
        console.log(`     Thumbnail: ${article.thumbnail_key ? 'Yes' : 'No'}`);
        console.log(`     Source ID: ${article.source_id}`);
        console.log('');
      });
    }
    
    // Check for thumbnail_id column specifically
    console.log('\n🔍 Checking for thumbnail_id column...');
    try {
      const thumbnailIdCheck = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'articles' AND column_name = 'thumbnail_id';
      `;
      
      if (thumbnailIdCheck.length > 0) {
        console.log('✅ thumbnail_id column exists');
      } else {
        console.log('❌ thumbnail_id column does NOT exist (this is expected)');
      }
    } catch (error) {
      console.log('❌ Error checking thumbnail_id:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkArticles();
